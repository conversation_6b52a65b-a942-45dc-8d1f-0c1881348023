'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

import { throttle } from 'lodash';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import About from '@/components/About';
import Education from '@/components/Education';
import Experience from '@/components/Experience';
import CoreCompetencies from '@/components/CoreCompetencies';
import Skills from '@/components/Skills';
import Domains from '@/components/Domains';
import Contact from '@/components/Contact';
import Footer from '@/components/Footer';



export default function Home() {
  const [scrollProgress, setScrollProgress] = useState(0);

  // Scroll progress indicator logic with throttling for performance
  useEffect(() => {
    const handleScroll = () => {
      const totalScroll = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const currentScroll = document.documentElement.scrollTop;
      const scrollPercentage = (currentScroll / totalScroll) * 100;
      setScrollProgress(scrollPercentage);
    };

    // Throttle the scroll event listener for better performance
    const throttledHandleScroll = throttle(handleScroll, 100);

    window.addEventListener('scroll', throttledHandleScroll);
    return () => window.removeEventListener('scroll', throttledHandleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Scroll Progress Indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-purple-600 z-50"
        style={{ width: `${scrollProgress}%` }}
        initial={{ width: '0%' }}
        animate={{ width: `${scrollProgress}%` }}
        transition={{ duration: 0.1 }}
      />

      {/* Animated Background */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <motion.div
          className="absolute w-96 h-96 bg-blue-200/20 dark:bg-blue-800/10 rounded-full -top-48 -left-48"
          animate={{
            x: [0, 100, 50, 0],
            y: [0, 50, 100, 0],
            scale: [1, 1.1, 0.9, 1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <motion.div
          className="absolute w-80 h-80 bg-purple-200/20 dark:bg-purple-800/10 rounded-full -bottom-40 -right-40"
          animate={{
            x: [100, 0, 50, 100],
            y: [50, 100, 0, 50],
            scale: [0.8, 1, 1.2, 0.8]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <motion.div
          className="absolute w-72 h-72 bg-teal-200/20 dark:bg-teal-800/10 rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          animate={{
            x: [50, 100, 0, 50],
            y: [100, 0, 50, 100],
            scale: [1.2, 0.8, 1, 1.2]
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      </div>

      <Header />
      <main>
        <Hero />
        <About />
        <Education />
        <Experience />
        <CoreCompetencies />
        <Skills />
        <Domains />



        <Contact />
      </main>
      <Footer />


    </div>
  );
}