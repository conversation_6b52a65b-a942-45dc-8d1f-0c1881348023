'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Navigation, Globe, Clock } from 'lucide-react';
import dynamic from 'next/dynamic';

// Dynamically import map components to avoid SSR issues
const MapContainer = dynamic(
  () => import('react-leaflet').then((mod) => mod.MapContainer),
  { ssr: false }
);

const TileLayer = dynamic(
  () => import('react-leaflet').then((mod) => mod.TileLayer),
  { ssr: false }
);

const Marker = dynamic(
  () => import('react-leaflet').then((mod) => mod.Marker),
  { ssr: false }
);

const Popup = dynamic(
  () => import('react-leaflet').then((mod) => mod.Popup),
  { ssr: false }
);

interface LocationMapProps {
  className?: string;
}

const LocationMap: React.FC<LocationMapProps> = ({ className = '' }) => {
  const [isClient, setIsClient] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Montreal coordinates (downtown area)
  const montrealCoords: [number, number] = [45.5017, -73.5673];

  useEffect(() => {
    setIsClient(true);
    // Simulate map loading delay for smooth animation
    const timer = setTimeout(() => setMapLoaded(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  const locationInfo = {
    city: 'Montreal',
    province: 'Quebec',
    country: 'Canada',
    timezone: 'EST (UTC-5)',
    coordinates: '45.5017°N, 73.5673°W'
  };

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut" as const
      }
    }
  };

  const mapVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 0.3,
        ease: "easeOut" as const
      }
    }
  };

  if (!isClient) {
    return (
      <div className={`bg-card rounded-2xl p-8 border border-border ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.3 }}
      className={`bg-card rounded-2xl p-8 border border-border hover:border-primary transition-all duration-500 hover:shadow-xl ${className}`}
    >
      {/* Location Header */}
      <div className="flex items-center gap-4 mb-6">
        <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-xl flex items-center justify-center">
          <MapPin className="w-6 h-6 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-foreground">Current Location</h3>
          <p className="text-primary font-semibold">Montreal, QC, Canada</p>
        </div>
      </div>

      {/* Location Details Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
          <Globe className="w-5 h-5 text-primary" />
          <div>
            <p className="text-sm text-muted-foreground">Province</p>
            <p className="font-medium text-foreground">{locationInfo.province}</p>
          </div>
        </div>
        <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
          <Clock className="w-5 h-5 text-primary" />
          <div>
            <p className="text-sm text-muted-foreground">Timezone</p>
            <p className="font-medium text-foreground">{locationInfo.timezone}</p>
          </div>
        </div>
      </div>

      {/* Interactive Map */}
      <motion.div
        variants={mapVariants}
        className="relative h-64 rounded-xl overflow-hidden border border-border"
      >
        {!mapLoaded && (
          <div className="absolute inset-0 bg-muted/50 flex items-center justify-center z-10">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="text-muted-foreground">Loading map...</span>
            </div>
          </div>
        )}
        
        <MapContainer
          center={montrealCoords}
          zoom={12}
          style={{ height: '100%', width: '100%' }}
          className="z-0"
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            className="dark:hue-rotate-180 dark:invert dark:brightness-95 dark:contrast-90"
          />
          <Marker position={montrealCoords}>
            <Popup>
              <div className="text-center p-2">
                <h4 className="font-bold text-gray-800">Montreal, QC</h4>
                <p className="text-sm text-gray-600">Adil's Current Location</p>
                <p className="text-xs text-gray-500 mt-1">{locationInfo.coordinates}</p>
              </div>
            </Popup>
          </Marker>
        </MapContainer>
      </motion.div>

      {/* Coordinates Display */}
      <div className="mt-4 flex items-center justify-between text-sm">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Navigation className="w-4 h-4" />
          <span>Coordinates: {locationInfo.coordinates}</span>
        </div>
        <div className="text-primary font-medium">
          Available for Remote/Hybrid/Onsite
        </div>
      </div>
    </motion.div>
  );
};

export default LocationMap;
