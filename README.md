# Personal Portfolio - Adil M. Faiyaz

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## New Features

### Editable Content

This portfolio now features editable content that allows you to modify:

- Personal information (name, title, bio)
- Key achievements
- Skills and skill levels
- Projects and project details
- Contact information

To edit content:
1. Click the "Edit Content" button in the bottom right corner
2. <PERSON>ver over any editable field to see the edit icon
3. Click the edit icon to modify the content
4. Click the checkmark to save your changes
5. Click "Save Changes" when you're done editing

All changes are saved to your browser's localStorage, so they will persist between sessions.

### AI Chatbox

The portfolio includes an AI chatbox that can answer questions about Adil M. Faiyaz. This feature:

- Provides automated responses to common questions about skills, experience, and projects
- Is accessible via the "Chat with AI" button in the bottom left corner
- Can be customized by editing the response generation logic in the AIChatbox component

### Google Maps Integration

The contact section now includes a Google Maps embed that shows the location (Montreal, QC).

## Contact Form Setup

This project includes a contact form that sends submissions to the email address: <EMAIL>.

### EmailJS Configuration

The contact form uses [EmailJS](https://www.emailjs.com/) to send emails directly from the client-side without requiring a backend server. To set up EmailJS:

1. Create an account on [EmailJS](https://www.emailjs.com/)
2. Create an Email Service (e.g., Gmail, Outlook, etc.)
3. Create an Email Template with the following template parameters:
   - `to_email`: The recipient's email (<EMAIL>)
   - `from_name`: The sender's name
   - `from_email`: The sender's email
   - `subject`: The email subject
   - `message`: The email message
4. Get your EmailJS service ID, template ID, and public key
5. Update the following values in `src/components/Contact.tsx`:
   ```javascript
   const serviceId = 'your_service_id';
   const templateId = 'your_template_id';
   const publicKey = 'your_public_key';
   ```

## Resume

To update your resume:
1. Replace the file at `/public/resume.pdf` with your updated resume
2. The "Download Resume" button in the Hero section will automatically link to the new file

## Project Structure

- `src/components/` - React components for each section of the portfolio
- `src/context/` - Context providers for theme and editable content
- `src/assets/` - Static assets like images and SVGs
- `src/App.tsx` - Main application component

### Key Components

- `EditableField.tsx` - Reusable component for editable content
- `EditModeToggle.tsx` - Toggle button for edit mode
- `AIChatbox.tsx` - AI chatbox component
- `Hero.tsx`, `About.tsx`, `Skills.tsx`, `Projects.tsx`, `Contact.tsx` - Main section components

### Context Providers

- `EditableContext.tsx` - Provides state and functions for editable content
- `ThemeContext.tsx` - Provides theme state and toggle function

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.