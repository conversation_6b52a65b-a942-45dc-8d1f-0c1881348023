{"name": "personal-portfolio", "version": "0.3.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@emailjs/browser": "^4.3.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@types/leaflet": "^1.9.19", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^12.23.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "^15.1.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-leaflet": "^5.0.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "web-vitals": "^2.1.4"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.13", "@types/node": "^20.11.30", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.4.5"}}