import React, { memo, useMemo, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

interface DomainEntry {
  id: string;
  title: string;
  achievement: string;
  description: string;
  color: string;
  industry: string;
}

const Domains: React.FC = () => {
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  const toggleCard = (id: string) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedCards(newExpanded);
  };

  const containerVariants = useMemo(() => ({
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.2,
        staggerChildren: 0.15,
      },
    },
  }), []);

  const itemVariants = useMemo(() => ({
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
      },
    },
  }), []);

  const cardHoverVariants = {
    hover: {
      scale: 1.02,
      y: -4,
      transition: { type: "spring" as const, stiffness: 300, damping: 20 }
    }
  };

  const domains: DomainEntry[] = [
    {
      id: 'business-listings',
      title: 'Business Listings and Advertisement Syndication',
      achievement: 'Advertisement Syndication Excellence',
      description: 'Experience with business directory platforms, advertising distribution systems, and syndication networks. Expertise in data syndication APIs, business listing management systems, and advertisement delivery platforms.',
      color: 'from-blue-500 to-blue-600',
      industry: 'Digital Marketing'
    },
    {
      id: 'workflow-automation',
      title: 'Workflow Automation',
      achievement: 'Process Automation Mastery',
      description: 'Building automated business processes and workflow systems to improve operational efficiency. Expertise in process automation tools, workflow engines, business rule engines, and integration platforms.',
      color: 'from-green-500 to-green-600',
      industry: 'Business Process'
    },
    {
      id: 'software-security',
      title: 'Software Security with Multi-Tenancy',
      achievement: 'Multi-Tenant Security Leadership',
      description: 'Developing and testing secure multi-tenant SaaS applications with proper tenant isolation, data security, and access controls. Expertise in tenant isolation patterns, secure coding practices, and multi-tenant architecture testing.',
      color: 'from-red-500 to-red-600',
      industry: 'Cybersecurity'
    },
    {
      id: 'iam',
      title: 'Identity and Access Management (IAM)',
      achievement: 'IAM Architecture Excellence',
      description: 'Implementing and testing authentication, authorization, user management, and identity verification systems. Expertise in OAuth, SAML, JWT tokens, RBAC, identity providers, and IAM automation testing.',
      color: 'from-purple-500 to-purple-600',
      industry: 'Identity Security'
    },
    {
      id: 'digital-signatures',
      title: 'Digital Signatures and Access Control',
      achievement: 'Digital Security Innovation',
      description: 'Working with digital signature verification, document security, and electronic signature workflows. Expertise in PKI, digital certificates, signature verification APIs, and document security frameworks.',
      color: 'from-teal-500 to-teal-600',
      industry: 'Document Security'
    },
    {
      id: 'fintech',
      title: 'Financial Technology and Payment Processing',
      achievement: 'FinTech Solutions Expertise',
      description: 'Experience with fintech applications, payment systems, and financial data processing. Expertise in payment gateways, financial APIs, transaction processing, and compliance testing.',
      color: 'from-orange-500 to-orange-600',
      industry: 'Financial Technology'
    },
    {
      id: 'healthcare-it',
      title: 'Medical Imaging and Healthcare IT Testing',
      achievement: 'Healthcare IT Excellence',
      description: 'Specialized testing for medical imaging systems, DICOM standards, PACS solutions, and healthcare IT infrastructure. Ensuring patient safety and compliance with radiological imaging software.',
      color: 'from-indigo-500 to-indigo-600',
      industry: 'Healthcare Technology'
    }
  ];

  return (
    <section id="domains" className="py-20 bg-gray-50 dark:bg-gray-800 relative overflow-hidden">
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-blue-50 rounded-bl-full opacity-70 -z-10"></div>
      <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-purple-50 rounded-tr-full opacity-70 -z-10"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Domain <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Expertise</span>
            </h2>
            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Deep industry knowledge across multiple domains, bringing specialized expertise to complex technical challenges
            </p>
          </motion.div>

          {/* Domain Expertise Cards */}
          <div className="space-y-8">
            {domains.map((domain, index) => (
              <motion.div
                key={domain.id}
                variants={itemVariants}
                whileHover="hover"
                className="group relative"
              >
                <motion.div
                  variants={cardHoverVariants}
                  className="bg-card/80 backdrop-blur-sm rounded-xl p-6 shadow-md hover:shadow-xl transition-all duration-500 border border-border/50 hover:border-primary/50 relative overflow-hidden group-hover:bg-card/90"
                  whileHover={{
                    scale: 1.02,
                    y: -4,
                    transition: { type: "spring", stiffness: 300, damping: 20 }
                  }}
                >
                  {/* Animated Border Effect */}
                  <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className={`absolute inset-0 rounded-xl bg-gradient-to-r ${domain.color} opacity-20 animate-pulse`}></div>
                    <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-pink-500/30 bg-clip-border animate-pulse"></div>
                  </div>

                  {/* Card Content */}
                  <div className="relative z-10">
                    {/* Domain Header - Always Visible */}
                    <div
                      className="mb-5 cursor-pointer"
                      onClick={() => toggleCard(domain.id)}
                    >
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-2 mb-3">
                        <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors duration-200">
                          {domain.achievement}
                        </h3>
                        <div className="flex items-center gap-3">
                          <span className="text-xs px-2.5 py-1 bg-primary/10 text-primary rounded-md font-medium">
                            {domain.industry}
                          </span>
                          <motion.div
                            animate={{ rotate: expandedCards.has(domain.id) ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                            className="text-muted-foreground hover:text-primary transition-colors"
                          >
                            <ChevronDown className="w-4 h-4" />
                          </motion.div>
                        </div>
                      </div>
                      <h4 className="text-base font-medium text-primary mb-2">{domain.title}</h4>
                    </div>

                    {/* Collapsible Description */}
                    <AnimatePresence>
                      {expandedCards.has(domain.id) && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="pt-4 border-t border-border/30">
                            <p className="text-muted-foreground leading-relaxed">
                              {domain.description}
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>

          <motion.div variants={itemVariants} className="text-center mt-16">
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Ready to leverage this domain expertise for your project?
            </p>
            <motion.a
              href="#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
            >
              Let's Discuss Your Needs
            </motion.a>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default memo(Domains);
