'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

interface ExperienceEntry {
  id: string;
  company: string;
  location: string;
  period: string;
  role: string;
  description: string;
  logo: string;
  color: string;
  industry: string;
}

const experienceData: ExperienceEntry[] = [
  {
    id: 'global-leadership',
    company: 'Global Project Cross Collaboration',
    location: 'Montreal, QC',
    period: '',
    role: 'Cross Collaborative Project Lead',
    description: 'Successfully coordinated and delivered high-impact projects across 6 international time zones, working seamlessly with distributed teams located in Chicago and New York (United States), Paris (France), Vienna (Austria), Dubai (United Arab Emirates), and Mumbai (India). Demonstrated exceptional cross-cultural communication skills and project management expertise in managing complex deliverables across diverse geographical locations and cultural contexts.',
    logo: '',
    color: 'from-purple-500 to-indigo-600',
    industry: 'Global and Remote Operations'
  },
  {
    id: 'swiftconnect',
    company: 'SwiftConnect Inc',
    location: 'Montreal, QC',
    period: '',
    role: 'Senior QA Engineer',
    description: 'Collaboration on design and implementation of test automation framework for Kafka-based event-driven systems using Golang. Focused on reliable message serialization via Protocol Buffers (Protobuf), and ensured message flow integrity across microservices with robust topic validation strategies. Contributed to functional and integration testing pipelines for Kafka topics and consumer groups. Additionally, documented system behavior, test strategies, and message schemas in Confluence to support ongoing development and cross-team knowledge sharing.',
    logo: '',
    color: 'from-green-500 to-emerald-600',
    industry: 'Access Control'
  },
  {
    id: 'onespan',
    company: 'OneSpan Inc',
    location: 'Montreal, QC',
    period: '',
    role: 'Senior Test Automation Engineer',
    description: 'Led specialized quality assurance initiatives for Identity and Access Management (IAM) systems and digital signature technologies. Engineered comprehensive test automation suites using Java Spring Boot framework, implemented contract testing strategies for microservices architectures following producer-consumer patterns, and conducted advanced performance testing using Gatling to ensure system scalability and reliability under high-load conditions.',
    logo: '',
    color: 'from-blue-500 to-cyan-600',
    industry: 'Security'
  },
  {
    id: 'intelerad',
    company: 'Intelerad Medical Systems',
    location: 'Montreal, QC',
    period: '',
    role: 'Test Automation Analyst',
    description: 'Executed comprehensive performance testing strategies including spike testing and load analysis for medical imaging systems. Developed cost-optimization scripts that significantly reduced AWS infrastructure expenses through intelligent resource management and automated scaling solutions. Designed and implemented integration testing frameworks while providing expert-level technical support to resolve complex client environment issues through advanced log analysis, system diagnostics, and configuration optimization.',
    logo: '',
    color: 'from-red-500 to-pink-600',
    industry: 'Medical Imaging'
  },
  {
    id: 'yellowpages',
    company: 'Yellow Pages',
    location: 'Montreal, QC',
    period: '',
    role: 'Consultant (CGI)',
    description: 'Architected and developed enterprise-scale test automation frameworks from ground up, successfully scaling to over 4,000 automated test cases using Python. Implemented comprehensive REST API testing solutions and SOAP UI integrations, established seamless CI/CD pipelines with TeamCity for continuous integration, and built sophisticated automation frameworks for ActiveMQ messaging queue systems to ensure reliable message processing and system integration.',
    logo: '',
    color: 'from-yellow-500 to-orange-600',
    industry: 'Digital Marketing'
  },
  {
    id: 'ca-technologies',
    company: 'CA Technologies (previously Automic Software Inc)',
    location: 'Montreal, QC',
    period: '',
    role: 'Test Engineer',
    description: 'Launched professional career specializing in enterprise workflow automation and comprehensive software testing methodologies. Gained foundational expertise in automated testing frameworks, enterprise software quality assurance processes, and workflow optimization strategies that established the technical foundation for advanced automation and DevOps practices in subsequent roles.',
    logo: '',
    color: 'from-purple-500 to-indigo-600',
    industry: 'Workload Automation'
  }
];

const Experience: React.FC = () => {
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  const toggleCard = (id: string) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedCards(newExpanded);
  };
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2, // AWS SageMaker style staggered animation
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94] as const, // Professional cubic-bezier easing
        type: "spring" as const,
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Enhanced card animation variants for staggered entrance
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 80,
      scale: 0.9
    },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.9,
        delay: index * 0.2, // 0.2s intervals between cards
        ease: [0.25, 0.46, 0.45, 0.94] as const,
        type: "spring" as const,
        stiffness: 80,
        damping: 20
      }
    })
  };

  const cardHoverVariants = {
    hover: {
      y: -4,
      scale: 1.01,
      transition: {
        type: "spring" as const,
        stiffness: 400,
        damping: 25,
        duration: 0.2
      }
    }
  };

  return (
    <section className="py-20 bg-background" id="experience">
      <div className="container mx-auto px-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-4">
              <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                Professional Journey
              </h2>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              10+ years of progressive experience across diverse industries, building expertise in QA automation and DevOps
            </p>
          </motion.div>

          {/* Experience Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-purple-600 to-pink-600 hidden lg:block" />

            {/* Experience Cards */}
            <div className="space-y-8">
              {experienceData.map((experience, index) => (
                <motion.div
                  key={experience.id}
                  variants={cardVariants}
                  custom={index}
                  whileHover="hover"
                  className="group relative"
                >
                  {/* Timeline Dot */}
                  <div className="absolute left-6 w-4 h-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full border-4 border-background hidden lg:block z-10" />
                  
                  <motion.div
                    variants={cardHoverVariants}
                    className="lg:ml-20 bg-card/80 backdrop-blur-sm rounded-xl p-6 shadow-md hover:shadow-xl transition-all duration-500 border border-border/50 hover:border-primary/50 relative overflow-hidden group-hover:bg-card/90"
                    whileHover={{
                      scale: 1.02,
                      y: -4,
                      transition: { type: "spring", stiffness: 300, damping: 20 }
                    }}
                  >
                    {/* Animated Border Effect */}
                    <div className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className={`absolute inset-0 rounded-xl bg-gradient-to-r ${experience.color} opacity-20 animate-pulse`}></div>
                      <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-pink-500/30 bg-clip-border animate-pulse"></div>
                    </div>

                    {/* Background Gradient Overlay */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${experience.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-xl`} />

                    {/* Enhanced Top Border Accent with Animation */}
                    <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${experience.color} opacity-60 group-hover:opacity-100 transition-all duration-500 group-hover:h-1.5`} />
                    
                    {/* Card Content */}
                    <div className="relative z-10">
                      {/* Company Header - Always Visible */}
                      <div
                        className="mb-5 cursor-pointer"
                        onClick={() => toggleCard(experience.id)}
                      >
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-2 mb-3">
                          <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors duration-200">
                            {experience.company}
                          </h3>
                          <div className="flex items-center gap-3">
                            <span className="text-xs px-2.5 py-1 bg-primary/10 text-primary rounded-md font-medium">
                              {experience.industry}
                            </span>
                            <motion.div
                              animate={{ rotate: expandedCards.has(experience.id) ? 180 : 0 }}
                              transition={{ duration: 0.2 }}
                              className="text-muted-foreground hover:text-primary transition-colors"
                            >
                              <ChevronDown className="w-4 h-4" />
                            </motion.div>
                          </div>
                        </div>
                        <h4 className="text-base font-medium text-primary mb-2">{experience.role}</h4>
                        <div className="text-muted-foreground text-sm">
                          <span>{experience.location}</span>
                        </div>
                      </div>

                      {/* Collapsible Description */}
                      <AnimatePresence>
                        {expandedCards.has(experience.id) && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="pt-4 border-t border-border/30">
                              <p className="text-muted-foreground leading-relaxed">
                                {experience.description}
                              </p>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>


        </motion.div>
      </div>
    </section>
  );
};

export default Experience;
