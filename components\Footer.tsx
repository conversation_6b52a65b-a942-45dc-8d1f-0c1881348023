import React from 'react';
import { motion } from 'framer-motion';
import { Github, Linkedin, Mail, Phone } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  const email = "<EMAIL>";
  const phone = "******-443-7486";

  const socialLinks = [
    { icon: Linkedin, href: 'https://www.linkedin.com/in/adilfaiyaz', label: 'LinkedIn' },
    { icon: Mail, href: `mailto:${email}`, label: 'Email' },
    { icon: Phone, href: `tel:${phone}`, label: 'Phone' },
  ];

  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-3 gap-8 mb-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Adil Faiyaz
            </h3>
            <p className="text-gray-300 leading-relaxed">
              Senior QA Engineer & DevOps Specialist passionate about automation, cloud technologies, and building secure, scalable systems.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-200">Quick Links</h4>
            <nav className="flex flex-col space-y-2">
              <a href="#home" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                Home
              </a>
              <a href="#about" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                About
              </a>
              <a href="#skills" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                Skills
              </a>
              <a href="#domains" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                Domains
              </a>
              <a href="#contact" className="text-gray-300 hover:text-blue-400 transition-colors duration-200">
                Contact
              </a>
            </nav>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-200">Get In Touch</h4>
            <div className="space-y-3">
              <a 
                href={`mailto:${email}`}
                className="flex items-center text-gray-300 hover:text-blue-400 transition-colors duration-200"
              >
                <Mail size={16} className="mr-2" />
                {email}
              </a>
              <a 
                href={`tel:${phone}`}
                className="flex items-center text-gray-300 hover:text-blue-400 transition-colors duration-200"
              >
                <Phone size={16} className="mr-2" />
                {phone}
              </a>
            </div>
            
            {/* Social Links */}
            <div className="flex gap-4 pt-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-10 h-10 bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white rounded-lg transition-all duration-200 flex items-center justify-center"
                  aria-label={social.label}
                >
                  <social.icon size={18} />
                </motion.a>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} Adil Faiyaz. All rights reserved.
            </p>

          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
