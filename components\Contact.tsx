import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import * as Dialog from '@radix-ui/react-dialog';
import { Mail, Phone, Send, Linkedin, X, MessageSquare, ChevronDown } from 'lucide-react';
import LocationMap from './LocationMap';

const Contact: React.FC = () => {
  const email = "<EMAIL>";
  const phone = "******-443-7486";
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    contactReason: '',
    subject: '',
    message: '',
  });

  const [formStatus, setFormStatus] = useState({
    submitted: false,
    success: false,
    message: '',
  });

  const [formErrors, setFormErrors] = useState({
    firstName: '',
    lastName: '',
    email: '',
    contactReason: '',
    subject: '',
    message: '',
  });

  // Contact reason options
  const contactReasons = [
    "Project-Based Services",
    "Full-Time Hiring Inquiry",
    "Availability for Work",
    "Expert Advice & Consulting",
    "Offshore Project Collaboration",
    "Other Professional Inquiry"
  ];

  // Validation functions
  const validateForm = useCallback(() => {
    const errors = {
      firstName: '',
      lastName: '',
      email: '',
      contactReason: '',
      subject: '',
      message: '',
    };

    // First name validation - mandatory, max 10 characters
    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    } else if (formData.firstName.length > 10) {
      errors.firstName = 'First name must be 10 characters or less';
    }

    // Last name validation - mandatory, max 10 characters
    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    } else if (formData.lastName.length > 10) {
      errors.lastName = 'Last name must be 10 characters or less';
    }

    // Email validation - mandatory, must be valid email
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        errors.email = 'Please enter a valid email address';
      }
    }

    // Contact reason validation - mandatory
    if (!formData.contactReason.trim()) {
      errors.contactReason = 'Please select a contact reason';
    }

    // Subject validation - mandatory, no character limit
    if (!formData.subject.trim()) {
      errors.subject = 'Subject is required';
    }

    // Message validation - mandatory, minimum 30 characters
    if (!formData.message.trim()) {
      errors.message = 'Message is required';
    } else if (formData.message.trim().length < 30) {
      errors.message = 'Message must be at least 30 characters long';
    }

    setFormErrors(errors);
    return Object.values(errors).every(error => error === '');
  }, [formData]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Apply character limits during typing
    let limitedValue = value;
    if ((name === 'firstName' || name === 'lastName') && value.length > 10) {
      limitedValue = value.slice(0, 10);
    }

    setFormData(prev => ({ ...prev, [name]: limitedValue }));

    // Clear specific field error when user starts typing
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  }, [formErrors]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      setFormStatus({
        submitted: false,
        success: false,
        message: 'Please fix the errors above before submitting.',
      });
      return;
    }

    setFormStatus({
      submitted: true,
      success: false,
      message: 'Sending message...',
    });

    try {
      // Using a simple mailto approach as fallback
      const fullName = `${formData.firstName} ${formData.lastName}`;
      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(`Portfolio Contact: ${formData.subject}`)}&body=${encodeURIComponent(`From: ${fullName}\nEmail: ${formData.email}\nContact Reason: ${formData.contactReason}\n\nMessage:\n${formData.message}`)}`;

      // Open mailto link
      window.open(mailtoLink, '_blank');

      setFormStatus({
        submitted: true,
        success: true,
        message: 'Your email client should open with the message pre-filled. If not, please email me <NAME_EMAIL>',
      });
    } catch (error) {
      console.error('Error opening email client:', error);
      setFormStatus({
        submitted: true,
        success: false,
        message: 'Please email me <NAME_EMAIL> with your message.',
      });
    }
  };

  useEffect(() => {
    if (formStatus.submitted) {
      const timeout = setTimeout(() => {
        setFormData({ firstName: '', lastName: '', email: '', contactReason: '', subject: '', message: '' });
        setFormStatus({ submitted: false, success: false, message: '' });
      }, 5000);
      return () => clearTimeout(timeout);
    }
  }, [formStatus.submitted]);

  const contactInfo = useMemo(() => [
    {
      icon: Mail,
      title: 'Email',
      value: email,
      href: `mailto:${email}`,
    },
    {
      icon: Phone,
      title: 'Phone',
      value: phone,
      href: `tel:${phone}`,
    },
  ], [email, phone]);

  const socialLinks = [
    { icon: Linkedin, href: 'https://www.linkedin.com/in/adilfaiyaz', label: 'LinkedIn' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { delayChildren: 0.3, staggerChildren: 0.2 },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.5 } },
  };

  return (
    <section id="contact" className="py-20 bg-muted/30 relative overflow-hidden">
      <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-blue-50 rounded-br-full opacity-70 -z-10"></div>
      <div className="absolute bottom-0 right-0 w-1/4 h-1/4 bg-purple-50 rounded-tl-full opacity-70 -z-10"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Get In <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Touch</span>
            </h2>
            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              I would love to hear from you
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div>
                <h3 className="text-2xl font-semibold mb-6 text-gray-800 dark:text-gray-200">
                  Let's Connect
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-8">
                  I'm at the moment open to discussing new opportunities, interesting projects, or just having a chat industry trends and technologies.
                </p>
              </div>

              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <motion.a
                    key={index}
                    href={info.href}
                    variants={itemVariants}
                    whileHover={{
                      x: 5,
                      scale: 1.02,
                      y: -2,
                      transition: { type: "spring", stiffness: 300, damping: 20 }
                    }}
                    className="group flex items-center p-4 bg-card rounded-lg shadow-md hover:shadow-xl transition-all duration-500 border border-border hover:border-primary/50 relative overflow-hidden"
                  >
                    {/* Animated Border Effect */}
                    <div className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 animate-pulse"></div>
                      <div className="absolute inset-0 rounded-lg border-2 border-transparent bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-pink-500/30 bg-clip-border animate-pulse"></div>
                    </div>

                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mr-4 relative z-10">
                      <info.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="relative z-10">
                      <h4 className="font-semibold text-gray-800 dark:text-gray-200 group-hover:text-primary transition-colors duration-300">{info.title}</h4>
                      <p className="text-gray-600 dark:text-gray-300">{info.value}</p>
                    </div>
                  </motion.a>
                ))}
              </div>

              <div className="pt-6">
                <h4 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">
                  Follow Me
                </h4>
                <div className="flex gap-4">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.1, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-12 h-12 bg-white dark:bg-gray-900 shadow-md hover:shadow-lg text-gray-600 hover:text-blue-600 rounded-lg transition-all duration-200 flex items-center justify-center"
                      aria-label={social.label}
                    >
                      <social.icon size={20} />
                    </motion.a>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Contact Form with Radix UI Dialog - Animated Button */}
            <motion.div variants={itemVariants}>
              <Dialog.Root>
                <Dialog.Trigger asChild>
                  <motion.div
                    className="relative inline-block cursor-pointer w-full h-40"
                    whileHover={{ scale: 1.55 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      className="relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 p-0.5 group h-full"
                    >
                      {/* Animated background border */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"
                        animate={{
                          backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                        style={{
                          backgroundSize: "200% 200%"
                        }}
                      />
                      {/* Animated pointer */}
                      <motion.div
                        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-4 h-4 rounded-full"
                        animate={{
                          scale: [1, 1.2, 1],
                          rotate: 360,
                          backgroundColor: [
                            "rgb(37, 99, 235)", 
                            "rgb(147, 51, 234)", 
                            "rgb(219, 39, 119)", 
                            "rgb(37, 99, 235)" 
                          ]
                        }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                      />
                      {/* Send Message Button */}
                      <motion.button
                        type="button"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="absolute inset-0 w-full h-full rounded-lg"
                      />
                      {/* Animated circular border */}
                      <motion.div
                        className="absolute inset-0 rounded-lg border-4 border-transparent"
                        style={{
                          background: `linear-gradient(90deg, transparent, rgba(255,255,255,0.3)) border-box`,
                          WebkitMask: 'linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',
                          mask: 'linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',
                        }}
                        animate={{
                          rotate: 360,
                          background: [
                            'linear-gradient(0deg, transparent 50%, rgba(255,255,255,0.3))',
                            'linear-gradient(360deg, transparent 50%, rgba(255,255,255,0.3))'
                          ]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                      />
                      {/* Dynamic background */}
                      <motion.div
                        className="absolute inset-0 rounded-lg"
                        animate={{
                          background: [
                            'linear-gradient(45deg, #3b82f6, #8b5cf6)',
                            'linear-gradient(45deg, #8b5cf6, #ec4899)',
                            'linear-gradient(45deg, #ec4899, #3b82f6)'
                          ]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                      />
                      <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6 flex items-center justify-center gap-3 transition-all duration-300 hover:shadow-lg">
                        <MessageSquare size={22} />
                        <span className="text-lg font-semibold">Send a Message</span>
                      </div>
                    </motion.div>
                  </motion.div>
                </Dialog.Trigger>
                <Dialog.Portal>
                  <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-md z-50" />
                  <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-card/95 backdrop-blur-xl rounded-3xl p-8 shadow-2xl max-w-2xl w-full mx-4 z-50 max-h-[90vh] overflow-y-auto border border-border/50 relative">
                    {/* Gradient border effect */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 -z-10"></div>

                    <Dialog.Title className="text-3xl font-bold mb-8 text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      Send a Message
                    </Dialog.Title>

                    <Dialog.Close asChild>
                      <motion.button
                        whileHover={{ scale: 1.1, rotate: 90 }}
                        whileTap={{ scale: 0.9 }}
                        className="absolute top-6 right-6 w-10 h-10 rounded-full bg-gray-100/80 dark:bg-gray-800/80 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 flex items-center justify-center transition-all duration-200 backdrop-blur-sm"
                        aria-label="Close"
                      >
                        <X size={18} />
                      </motion.button>
                    </Dialog.Close>

                    <form onSubmit={handleSubmit} className="space-y-6">

                {/* Name Fields - Split into First and Last Name */}
                <div className="grid md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      First Name <span className="text-red-500">*</span>
                      <span className="text-xs text-gray-500 ml-2">({formData.firstName.length}/10)</span>
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      required
                      maxLength={10}
                      className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors duration-200 ${
                        formErrors.firstName
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                      }`}
                    />
                    {formErrors.firstName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.firstName}</p>
                    )}
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Last Name <span className="text-red-500">*</span>
                      <span className="text-xs text-gray-500 ml-2">({formData.lastName.length}/10)</span>
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      required
                      maxLength={10}
                      className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors duration-200 ${
                        formErrors.lastName
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                      }`}
                    />
                    {formErrors.lastName && (
                      <p className="text-red-500 text-sm mt-1">{formErrors.lastName}</p>
                    )}
                  </div>
                </div>

                {/* Email Field */}
                <div className="mb-6">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors duration-200 ${
                      formErrors.email
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                    }`}
                  />
                  {formErrors.email && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
                  )}
                </div>

                {/* Contact Reason Dropdown */}
                <div className="mb-6">
                  <label htmlFor="contactReason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Contact Reason <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id="contactReason"
                      name="contactReason"
                      value={formData.contactReason}
                      onChange={handleChange}
                      required
                      className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors duration-200 appearance-none ${
                        formErrors.contactReason
                          ? 'border-red-500 focus:ring-red-500'
                          : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                      }`}
                    >
                      <option value="">Select a reason for contact</option>
                      {contactReasons.map((reason, index) => (
                        <option key={index} value={reason}>
                          {reason}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                  </div>
                  {formErrors.contactReason && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.contactReason}</p>
                  )}
                </div>

                {/* Subject Field */}
                <div className="mb-6">
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Subject <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors duration-200 ${
                      formErrors.subject
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                    }`}
                    placeholder="Enter your subject line"
                  />
                  {formErrors.subject && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.subject}</p>
                  )}
                </div>

                {/* Message Field */}
                <div className="mb-6">
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Message <span className="text-red-500">*</span>
                    <span className="text-xs text-gray-500 ml-2">(min. 30 characters - {formData.message.length} entered)</span>
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={5}
                    className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:border-transparent dark:bg-gray-800 dark:text-white transition-colors duration-200 resize-vertical ${
                      formErrors.message
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                    }`}
                    placeholder="Enter your message here..."
                  />
                  {formErrors.message && (
                    <p className="text-red-500 text-sm mt-1">{formErrors.message}</p>
                  )}
                </div>

                {/* Modern Message Popup */}
                <AnimatePresence>
                  {formStatus.message && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      className={`mb-6 p-4 rounded-2xl shadow-lg border backdrop-blur-sm ${
                        formStatus.success
                          ? 'bg-green-50/90 text-green-800 border-green-200 dark:bg-green-900/50 dark:text-green-200 dark:border-green-700'
                          : formStatus.submitted
                            ? 'bg-blue-50/90 text-blue-800 border-blue-200 dark:bg-blue-900/50 dark:text-blue-200 dark:border-blue-700'
                            : 'bg-red-50/90 text-red-800 border-red-200 dark:bg-red-900/50 dark:text-red-200 dark:border-red-700'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          formStatus.success
                            ? 'bg-green-500'
                            : formStatus.submitted
                              ? 'bg-blue-500 animate-pulse'
                              : 'bg-red-500'
                        }`} />
                        <p className="text-sm leading-relaxed font-medium">
                          {formStatus.message}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Send Message Button */}
                <motion.div
                  className="relative inline-block cursor-pointer w-full"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.button
                    type="submit"
                    disabled={formStatus.submitted}
                    className="relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 p-0.5 group w-full disabled:opacity-50"
                  >
                          {/* Animated background border */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"
                            animate={{
                              backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
                            }}
                            transition={{
                              duration: 3,
                              repeat: Infinity,
                              ease: "linear"
                            }}
                            style={{
                              backgroundSize: "200% 200%"
                            }}
                          />

                          {/* Button content */}
                          <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg py-3 px-6 flex items-center justify-center gap-2 transition-all duration-300 hover:shadow-lg">
                            <Send size={18} />
                            <span className="font-medium">
                              {formStatus.submitted ? 'Opening Email Client...' : 'Send Message'}
                            </span>
                          </div>
                        </motion.button>
                      </motion.div>
                    </form>
                  </Dialog.Content>
                </Dialog.Portal>
              </Dialog.Root>
            </motion.div>
          </div>

          {/* Location Map Section */}
          <motion.div variants={itemVariants} className="mt-16">
            <LocationMap />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default memo(Contact);
