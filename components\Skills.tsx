import React, { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import * as Tabs from '@radix-ui/react-tabs';
import { InteractiveCard, AnimatedBorder } from './ui/interactive-card';
import {
  Code,
  Server,
  Shield,
  Cloud,
  Container,
  TestTube,
  Gauge,
  Lock,
  FileCheck,
  MessageSquare,
  Network,
  Key,
  Sparkles,
  Database,
  Brain,
  Puzzle,
  Settings,
  Monitor,
  Globe,
  Zap
} from 'lucide-react';

interface SkillSubsection {
  name: string;
  skills: string[];
}

interface SkillCategory {
  title: string;
  color: string;
  skills?: string[];
  subsections?: SkillSubsection[];
}

const getIconForCategory = (title: string) => {
  const iconMap: { [key: string]: React.ReactElement } = {
    "Languages": <Code className="w-7 h-7 text-white" />,
    "Backend Development": <Server className="w-7 h-7 text-white" />,
    "Containerization": <Container className="w-7 h-7 text-white" />,
    "Quality Assurance": <TestTube className="w-7 h-7 text-white" />,
    "Performance Testing": <Gauge className="w-7 h-7 text-white" />,
    "Security Testing": <Shield className="w-7 h-7 text-white" />,
    "Compliance and Regulatory Frameworks": <FileCheck className="w-7 h-7 text-white" />,
    "Cloud & DevOps": <Cloud className="w-7 h-7 text-white" />,
    "Cloud Platforms & Services": <Globe className="w-7 h-7 text-white" />,
    "Testing & Quality Automation": <Settings className="w-7 h-7 text-white" />,
    "Messaging & Events": <MessageSquare className="w-7 h-7 text-white" />,
    "Service Mesh": <Network className="w-7 h-7 text-white" />,
    "Secrets Management": <Key className="w-7 h-7 text-white" />,
    "AI/ML Expertise": <Brain className="w-7 h-7 text-white" />,
    "Specialized Skills": <Sparkles className="w-7 h-7 text-white" />
  };
  return iconMap[title] || <Code className="w-7 h-7 text-white" />;
};

const Skills: React.FC = () => {
  const containerVariants = useMemo(() => ({
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.2,
        staggerChildren: 0.08,
      },
    },
  }), []);

  const itemVariants = useMemo(() => ({
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
      },
    },
  }), []);

  const skillCategories = [
    {
      title: "Languages",
      color: "from-blue-500 to-blue-600",
      skills: ["Java", "Python", "Golang", "Rust (Basic)", "Linux/Bash", "SQL", "TypeScript", "HTML", "CSS"]
    },
    {
      title: "Backend Development",
      color: "from-orange-500 to-orange-600",
      skills: ["Microservices", "APIs", "Cloud-native design", "Distributed systems", "RESTful backend services"]
    },
    {
      title: "Containerization",
      color: "from-cyan-500 to-cyan-600",
      skills: [
        "Docker", "Kubernetes", "Helm", "Service Discovery", "Ingress", "ArgoCD",
        "Terraform", "CI/CD", "GitHub Actions", "GitLab CI/CD", "Jenkins", "TeamCity"
      ],
      subsections: [
        {
          name: "Container Security",
          skills: ["Trivy", "Grype", "Clair", "Dockle", "Falco", "Kyverno", "Sigstore (Cosign)"]
        }
      ]
    },
    {
      title: "Quality Assurance",
      color: "from-indigo-500 to-indigo-600",
      subsections: [
        {
          name: "Testing & QA",
          skills: [
            "Functionality testing", "Regression testing", "Exploratory testing", "UI/UX testing",
            "Playwright", "Pytest", "TestNG", "RestAssured", "Requests API", "Terratest",
            "Ginkgo", "Gomega", "Parallel test execution", "Selenium", "Kafka testing",
            "Message queue testing", "API testing", "ETL testing"
          ]
        }
      ]
    },
    {
      title: "Performance Testing",
      color: "from-yellow-500 to-yellow-600",
      skills: [
        "Gatling", "JMeter", "k6", "In-house proprietary tools", "Realistic load testing",
        "Spike testing", "Performance benchmarking", "Grafana dashboard correlation", "Iterative tuning"
      ]
    },
    {
      title: "Security Testing",
      color: "from-red-500 to-red-600",
      skills: [
        "Agentless security scans", "Shift-left security", "Infrastructure validation with terratest",
        "IAM misconfigurations", "Privilege escalation", "Container security", "Digital signatures",
        "RBAC", "Encryption protocols", "TLS", "CI/CD security integration",
        "Authentication and Authorization (JWT, API keys, Rate limiting)"
      ]
    },
    {
      title: "Compliance and Regulatory Frameworks",
      color: "from-purple-500 to-purple-600",
      skills: [
        "PIPEDA", "GDPR", "FINTRAC", "FCAC", "SOC2", "PHI (Protected Health Information)",
        "NIST (800-53, CSF, 800-171)", "ISO/IEC 27001"
      ]
    },
    {
      title: "Cloud & DevOps",
      color: "from-teal-500 to-teal-600",
      subsections: [
        {
          name: "Cloud",
          skills: [
            "AWS IAM", "EC2", "VPC", "S3", "CloudWatch", "Route53", "RDS", "DynamoDB",
            "AWS Lambda", "EKS", "SQS", "AWS CLI", "boto3 SDK", "SageMaker GroundTruth",
            "GuardDuty", "WAF", "ELB", "ECR"
          ]
        },
        {
          name: "DevOps",
          skills: ["CI/CD & Automation (ArgoCD)", "IaC (Terraform, Pulumi)", "Containerization tools"]
        },
        {
          name: "Monitoring & Observability",
          skills: ["Prometheus", "Grafana", "Loki", "ELK Stack", "OpenTelemetry"]
        },
        {
          name: "Security & Compliance Tools",
          skills: [
            "Trivy", "Grype", "Aqua", "Snyk", "AWS Inspector", "HashiCorp Vault",
            "SBOM", "CVE", "Secret rotation", "Secure injection", "Compliance as Code (Checkov)"
          ]
        }
      ]
    },
    {
      title: "Cloud Platforms & Services",
      color: "from-sky-500 to-sky-600",
      skills: [
        "AWS", "Azure", "GCP", "OCI", "VPC networking", "IAM policies", "Auto-scaling",
        "Cost optimization", "Local testing simulations", "Cloud-native services like Lambda"
      ]
    },
    {
      title: "Testing & Quality Automation",
      color: "from-green-500 to-green-600",
      skills: [
        "Testcontainers", "Selenium Grid", "Postman", "K6", "Locust", "Shift-left testing in CI/CD",
        "Load and chaos testing", "Automated integration test orchestration in pipelines (using K6, Postman, Selenium, Testcontainers)"
      ]
    },
    {
      title: "Messaging & Events",
      color: "from-pink-500 to-pink-600",
      skills: ["Kafka", "NATS", "SQS"]
    },
    {
      title: "Service Mesh",
      color: "from-orange-500 to-orange-600",
      skills: ["Istio", "AppMesh with sidecar proxies for mTLS"]
    },
    {
      title: "Secrets Management",
      color: "from-gray-500 to-gray-600",
      skills: ["HashiCorp Vault"]
    },
    {
      title: "Specialized Skills",
      color: "from-emerald-500 to-emerald-600",
      subsections: [
        {
          name: "Data & Analytics",
          skills: ["Oracle Database", "SQL", "Data pipeline testing", "ETL testing", "Database performance"]
        },
        {
          name: "Design Patterns",
          skills: [
            "Page Object Model (POM)", "PageFactory", "Singleton Pattern", "Factory Pattern",
            "Builder Pattern", "Observer Pattern", "Dependency Injection"
          ]
        },
        {
          name: "AI Testing",
          skills: [
            "Prompt Injection vulnerabilities", "PII and proprietary data leakage",
            "Risks in LLM supply chains (biased outputs, breaches, system failures)",
            "Improper output handling", "Permission-based agent overreach in AI workflows"
          ]
        }
      ]
    },
    {
      title: "AI/ML Expertise",
      color: "from-violet-500 to-violet-600",
      subsections: [
        {
          name: "Core Concepts",
          skills: [
            "Supervised & Unsupervised Learning", "Model Evaluation (neural-scope library on PyPI)",
            "Prompt Engineering", "Agentic Workflows", "Model Packaging & Deployment", "MLOps",
            "Model Registry", "Drift Detection", "Explainable AI (XAI)", "Bias & Fairness Auditing"
          ]
        },
        {
          name: "Anomaly Detection Algorithms",
          skills: [
            "Isolation Forest", "Autoencoders", "k-means", "DBSCAN",
            "Use Cases: Fraud detection, Intrusion detection, System health"
          ]
        },
        {
          name: "Time Series Models",
          skills: ["Prophet", "XGBoost", "RandomForest Regressor"]
        },
        {
          name: "Deep Learning Frameworks",
          skills: ["PyTorch", "TensorFlow", "Transformers"]
        },
        {
          name: "Evaluation Skills",
          skills: [
            "Bias-variance tradeoff", "Underfitting/overfitting", "Cross-validation",
            "Metrics (Precision, Recall, F1 Score, ROC-AUC)"
          ]
        }
      ]
    }
  ];

  const certifications = [
    "Google AI Essentials Certificate",
    "Google Cybersecurity Certificate",
    "Oracle Database Administration (2011)"
  ];

  return (
    <section id="skills" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Technical <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Skills</span>
            </h2>
            <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              A comprehensive toolkit built through 10 years of hands-on experience in automation, cloud technologies, and quality engineering
            </p>
          </motion.div>

          {/* Radix UI Tabs for organized skill display */}
          <Tabs.Root defaultValue="Core Technologies" className="w-full mb-16">
            <Tabs.List className="flex justify-center mb-8 bg-card rounded-lg p-1 shadow-lg border border-border">
              <Tabs.Trigger
                value="Core Technologies"
                className="px-6 py-3 rounded-md text-sm font-medium transition-all duration-200 data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-blue-600 dark:data-[state=inactive]:text-gray-300 dark:data-[state=inactive]:hover:text-blue-400"
              >
                Core Technologies
              </Tabs.Trigger>
              <Tabs.Trigger
                value="Testing & Security"
                className="px-6 py-3 rounded-md text-sm font-medium transition-all duration-200 data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-blue-600 dark:data-[state=inactive]:text-gray-300 dark:data-[state=inactive]:hover:text-blue-400"
              >
                Testing & Security
              </Tabs.Trigger>
              <Tabs.Trigger
                value="Cloud & Infrastructure"
                className="px-6 py-3 rounded-md text-sm font-medium transition-all duration-200 data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-blue-600 dark:data-[state=inactive]:text-gray-300 dark:data-[state=inactive]:hover:text-blue-400"
              >
                Cloud & Infrastructure
              </Tabs.Trigger>
              <Tabs.Trigger
                value="AI & Specialized"
                className="px-6 py-3 rounded-md text-sm font-medium transition-all duration-200 data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-blue-600 dark:data-[state=inactive]:text-gray-300 dark:data-[state=inactive]:hover:text-blue-400"
              >
                AI & Specialized
              </Tabs.Trigger>
            </Tabs.List>

            <Tabs.Content value="Core Technologies">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {skillCategories.slice(0, 3).map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1, duration: 0.4 }}
                    whileHover={{
                      y: -8,
                      scale: 1.03,
                      rotateY: 5,
                      transition: { type: "spring", stiffness: 300, damping: 20 }
                    }}
                    className="bg-card/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-border/50 hover:border-primary/50 relative overflow-hidden group"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
                    }}
                  >
                    {/* Moving Animated border effect */}
                    <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 animate-pulse"></div>
                      <div className="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-blue-500/40 via-purple-500/40 to-pink-500/40 bg-clip-border animate-spin-slow"></div>
                      <div className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-blue-600/30 via-purple-600/30 to-pink-600/30 blur-sm animate-pulse"></div>
                    </div>

                    <motion.div
                      className={`w-14 h-14 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mb-4 relative z-10`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 200, duration: 0.6 }}
                    >
                      {getIconForCategory(category.title)}
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200 relative z-10">
                      {category.title}
                    </h3>
                    <div className="space-y-4 relative z-10">
                      {category.skills && (
                        <div className="flex flex-wrap gap-2">
                          {category.skills.map((skill, skillIndex) => (
                            <motion.span
                              key={skillIndex}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                              whileHover={{ scale: 1.05, y: -2 }}
                              className="px-3 py-1.5 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-sm cursor-default shadow-sm hover:shadow-md transition-all duration-200"
                            >
                              {skill}
                            </motion.span>
                          ))}
                        </div>
                      )}
                      {category.subsections && category.subsections.map((subsection, subIndex) => (
                        <motion.div
                          key={subIndex}
                          className="mt-4 p-4 bg-gradient-to-r from-gray-50/50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-700/50 rounded-xl border border-gray-200/50 dark:border-gray-600/50 relative group/subsection"
                          whileHover={{ scale: 1.02 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          {/* Subsection hover border effect */}
                          <div className="absolute inset-0 rounded-xl opacity-0 group-hover/subsection:opacity-100 transition-opacity duration-300">
                            <div className="absolute inset-0 rounded-xl border border-blue-400/50 dark:border-blue-500/50 animate-pulse"></div>
                          </div>

                          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2 relative z-10">
                            <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
                            {subsection.name}
                          </h4>
                          <div className="flex flex-wrap gap-2 relative z-10">
                            {subsection.skills.map((skill, skillIndex) => (
                              <motion.span
                                key={skillIndex}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                                whileHover={{ scale: 1.08, y: -3 }}
                                className="px-3 py-1.5 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/40 dark:to-purple-900/40 text-blue-800 dark:text-blue-200 rounded-full text-sm cursor-default shadow-sm hover:shadow-lg transition-all duration-200 border border-blue-200/50 dark:border-blue-700/50"
                              >
                                {skill}
                              </motion.span>
                            ))}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </Tabs.Content>

            <Tabs.Content value="Testing & Security">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {skillCategories.slice(3, 9).map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1, duration: 0.4 }}
                    whileHover={{
                      y: -8,
                      scale: 1.03,
                      rotateY: 5,
                      transition: { type: "spring", stiffness: 300, damping: 20 }
                    }}
                    className="bg-card/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-border/50 hover:border-primary/50 relative overflow-hidden group"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
                    }}
                  >
                    {/* Moving Animated border effect */}
                    <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-green-500/20 via-emerald-500/20 to-teal-500/20 animate-pulse"></div>
                      <div className="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-green-500/40 via-emerald-500/40 to-teal-500/40 bg-clip-border animate-spin-slow"></div>
                      <div className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-green-600/30 via-emerald-600/30 to-teal-600/30 blur-sm animate-pulse"></div>
                    </div>

                    <motion.div
                      className={`w-14 h-14 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mb-4 relative z-10`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 200, duration: 0.6 }}
                    >
                      {getIconForCategory(category.title)}
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200 relative z-10">
                      {category.title}
                    </h3>
                    <div className="space-y-4 relative z-10">
                      {category.skills && (
                        <div className="flex flex-wrap gap-2">
                          {category.skills.map((skill, skillIndex) => (
                            <motion.span
                              key={skillIndex}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                              whileHover={{ scale: 1.05, y: -2 }}
                              className="px-3 py-1.5 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-sm cursor-default shadow-sm hover:shadow-md transition-all duration-200"
                            >
                              {skill}
                            </motion.span>
                          ))}
                        </div>
                      )}
                      {category.subsections && category.subsections.map((subsection, subIndex) => (
                        <motion.div
                          key={subIndex}
                          className="mt-4 p-4 bg-gradient-to-r from-green-50/50 to-emerald-50/50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200/50 dark:border-green-700/50 relative group/subsection"
                          whileHover={{ scale: 1.02 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          {/* Subsection hover border effect */}
                          <div className="absolute inset-0 rounded-xl opacity-0 group-hover/subsection:opacity-100 transition-opacity duration-300">
                            <div className="absolute inset-0 rounded-xl border border-green-400/50 dark:border-green-500/50 animate-pulse"></div>
                          </div>

                          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2 relative z-10">
                            <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full animate-pulse"></div>
                            {subsection.name}
                          </h4>
                          <div className="flex flex-wrap gap-2 relative z-10">
                            {subsection.skills.map((skill, skillIndex) => (
                              <motion.span
                                key={skillIndex}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                                whileHover={{ scale: 1.08, y: -3 }}
                                className="px-3 py-1.5 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/40 dark:to-emerald-900/40 text-green-800 dark:text-green-200 rounded-full text-sm cursor-default shadow-sm hover:shadow-lg transition-all duration-200 border border-green-200/50 dark:border-green-700/50"
                              >
                                {skill}
                              </motion.span>
                            ))}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </Tabs.Content>

            <Tabs.Content value="Cloud & Infrastructure">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {skillCategories.slice(9, 15).map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1, duration: 0.4 }}
                    whileHover={{
                      y: -8,
                      scale: 1.03,
                      rotateY: 5,
                      transition: { type: "spring", stiffness: 300, damping: 20 }
                    }}
                    className="bg-card/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-border/50 hover:border-primary/50 relative overflow-hidden group"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
                    }}
                  >
                    {/* Moving Animated border effect */}
                    <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-500/20 via-amber-500/20 to-yellow-500/20 animate-pulse"></div>
                      <div className="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-orange-500/40 via-amber-500/40 to-yellow-500/40 bg-clip-border animate-spin-slow"></div>
                      <div className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-orange-600/30 via-amber-600/30 to-yellow-600/30 blur-sm animate-pulse"></div>
                    </div>

                    <motion.div
                      className={`w-14 h-14 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mb-4 relative z-10`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 200, duration: 0.6 }}
                    >
                      {getIconForCategory(category.title)}
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200 relative z-10">
                      {category.title}
                    </h3>
                    <div className="space-y-4 relative z-10">
                      {category.skills && (
                        <div className="flex flex-wrap gap-2">
                          {category.skills.map((skill, skillIndex) => (
                            <motion.span
                              key={skillIndex}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                              whileHover={{ scale: 1.05, y: -2 }}
                              className="px-3 py-1.5 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-sm cursor-default shadow-sm hover:shadow-md transition-all duration-200"
                            >
                              {skill}
                            </motion.span>
                          ))}
                        </div>
                      )}
                      {category.subsections && category.subsections.map((subsection, subIndex) => (
                        <motion.div
                          key={subIndex}
                          className="mt-4 p-4 bg-gradient-to-r from-orange-50/50 to-amber-50/50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-xl border border-orange-200/50 dark:border-orange-700/50 relative group/subsection"
                          whileHover={{ scale: 1.02 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          {/* Subsection hover border effect */}
                          <div className="absolute inset-0 rounded-xl opacity-0 group-hover/subsection:opacity-100 transition-opacity duration-300">
                            <div className="absolute inset-0 rounded-xl border border-orange-400/50 dark:border-orange-500/50 animate-pulse"></div>
                          </div>

                          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2 relative z-10">
                            <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-amber-500 rounded-full animate-pulse"></div>
                            {subsection.name}
                          </h4>
                          <div className="flex flex-wrap gap-2 relative z-10">
                            {subsection.skills.map((skill, skillIndex) => (
                              <motion.span
                                key={skillIndex}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                                whileHover={{ scale: 1.08, y: -3 }}
                                className="px-3 py-1.5 bg-gradient-to-r from-orange-100 to-amber-100 dark:from-orange-900/40 dark:to-amber-900/40 text-orange-800 dark:text-orange-200 rounded-full text-sm cursor-default shadow-sm hover:shadow-lg transition-all duration-200 border border-orange-200/50 dark:border-orange-700/50"
                              >
                                {skill}
                              </motion.span>
                            ))}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </Tabs.Content>

            <Tabs.Content value="AI & Specialized">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
              >
                {skillCategories.slice(15).map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1, duration: 0.4 }}
                    whileHover={{
                      y: -8,
                      scale: 1.03,
                      rotateY: 5,
                      transition: { type: "spring", stiffness: 300, damping: 20 }
                    }}
                    className="bg-card/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-border/50 hover:border-primary/50 relative overflow-hidden group"
                    style={{
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
                    }}
                  >
                    {/* Moving Animated border effect */}
                    <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-violet-500/20 via-purple-500/20 to-pink-500/20 animate-pulse"></div>
                      <div className="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-violet-500/40 via-purple-500/40 to-pink-500/40 bg-clip-border animate-spin-slow"></div>
                      <div className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-violet-600/30 via-purple-600/30 to-pink-600/30 blur-sm animate-pulse"></div>
                    </div>

                    <motion.div
                      className={`w-14 h-14 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mb-4 relative z-10`}
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 200, duration: 0.6 }}
                    >
                      {getIconForCategory(category.title)}
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200 relative z-10">
                      {category.title}
                    </h3>
                    <div className="space-y-4 relative z-10">
                      {category.skills && (
                        <div className="flex flex-wrap gap-2">
                          {category.skills.map((skill, skillIndex) => (
                            <motion.span
                              key={skillIndex}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                              whileHover={{ scale: 1.05, y: -2 }}
                              className="px-3 py-1.5 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-full text-sm cursor-default shadow-sm hover:shadow-md transition-all duration-200"
                            >
                              {skill}
                            </motion.span>
                          ))}
                        </div>
                      )}
                      {category.subsections && category.subsections.map((subsection, subIndex) => (
                        <motion.div
                          key={subIndex}
                          className="mt-4 p-4 bg-gradient-to-r from-violet-50/50 to-purple-50/50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-xl border border-violet-200/50 dark:border-violet-700/50 relative group/subsection"
                          whileHover={{ scale: 1.02 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          {/* Subsection hover border effect */}
                          <div className="absolute inset-0 rounded-xl opacity-0 group-hover/subsection:opacity-100 transition-opacity duration-300">
                            <div className="absolute inset-0 rounded-xl border border-violet-400/50 dark:border-violet-500/50 animate-pulse"></div>
                          </div>

                          <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2 relative z-10">
                            <div className="w-2 h-2 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full animate-pulse"></div>
                            {subsection.name}
                          </h4>
                          <div className="flex flex-wrap gap-2 relative z-10">
                            {subsection.skills.map((skill, skillIndex) => (
                              <motion.span
                                key={skillIndex}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: (index * 0.1) + (skillIndex * 0.05), duration: 0.3 }}
                                whileHover={{ scale: 1.08, y: -3 }}
                                className="px-3 py-1.5 bg-gradient-to-r from-violet-100 to-purple-100 dark:from-violet-900/40 dark:to-purple-900/40 text-violet-800 dark:text-violet-200 rounded-full text-sm cursor-default shadow-sm hover:shadow-lg transition-all duration-200 border border-violet-200/50 dark:border-violet-700/50"
                              >
                                {skill}
                              </motion.span>
                            ))}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </Tabs.Content>
          </Tabs.Root>

          {/* Certifications Section */}
          <motion.div variants={itemVariants} className="bg-card rounded-xl p-8 shadow-lg border border-border">
            <h3 className="text-2xl font-semibold mb-6 text-center text-gray-800 dark:text-gray-200">
              Professional Certifications
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {certifications.map((cert, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span className="text-gray-700 dark:text-gray-300">{cert}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default memo(Skills);
