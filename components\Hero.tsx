import React, { memo } from 'react';
import { motion } from 'framer-motion';
import * as Avatar from '@radix-ui/react-avatar';
import { ArrowDown, ExternalLink, Linkedin, Mail, Phone, User } from 'lucide-react';
import { MagneticButton, NeonButton } from './ui/magnetic-button';
import { InteractiveCard, FloatingParticles } from './ui/interactive-card';

const Hero: React.FC = () => {
  const name = "Adil Faiyaz";
  const title = "Automation QA Engineer | DevOps & CI/CD Specialist | Cloud & Security Aware";
  const email = "<EMAIL>";
  const phone = "******-443-7486";

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const socialLinks = [
    { icon: Linkedin, href: 'https://www.linkedin.com/in/adilfaiyaz', label: 'LinkedIn' },
    { icon: Mail, href: `mailto:${email}`, label: 'Email' },
    { icon: Phone, href: `tel:${phone}`, label: 'Phone' },
  ];

  return (
    <section id="home" className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 pt-16 relative overflow-hidden">
      {/* Floating Particles */}
      <FloatingParticles count={8} className="z-0" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid lg:grid-cols-2 gap-12 items-center"
        >
          <motion.div variants={containerVariants} className="text-left lg:order-1 order-2">
            <motion.div variants={itemVariants} className="mb-6 flex items-center gap-4">
              <Avatar.Root className="inline-flex h-16 w-16 select-none items-center justify-center overflow-hidden rounded-full bg-gradient-to-r from-blue-600 to-purple-600">
                <Avatar.Image
                  className="h-full w-full rounded-[inherit] object-cover"
                  src="/api/placeholder/64/64"
                  alt="Adil Faiyaz"
                />
                <Avatar.Fallback
                  className="text-white text-xl font-semibold flex h-full w-full items-center justify-center bg-gradient-to-r from-blue-600 to-purple-600"
                  delayMs={600}
                >
                  <User size={24} />
                </Avatar.Fallback>
              </Avatar.Root>
              <span className="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
                👋 Hello, I'm Adil
              </span>
            </motion.div>

            <motion.h1 variants={itemVariants} className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
              <motion.span
                className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent inline-block"
                animate={{
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "linear"
                }}
                style={{
                  backgroundSize: "200% 200%"
                }}
              >
                {name.split('').map((char, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      delay: 0.5 + index * 0.1,
                      duration: 0.5,
                      type: "spring",
                      stiffness: 100
                    }}
                    whileHover={{
                      scale: 1.2,
                      color: "#3B82F6",
                      transition: { duration: 0.2 }
                    }}
                    className="inline-block cursor-default"
                  >
                    {char === ' ' ? '\u00A0' : char}
                  </motion.span>
                ))}
              </motion.span>
            </motion.h1>

            <motion.h2 variants={itemVariants} className="text-xl md:text-2xl lg:text-3xl text-muted-foreground mb-8 font-light">
              {title}
            </motion.h2>

            <motion.p variants={itemVariants} className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl leading-relaxed">
              Passionate engineer focused on building reliable, scalable, and secure systems through automation, DevOps, and continuous quality.
            </motion.p>

            <motion.p variants={itemVariants} className="text-base md:text-lg text-muted-foreground mb-8 max-w-2xl leading-relaxed">
              With a deep focus on CI/CD, infrastructure-as-code, and observability, I help teams deliver faster and more reliably. My approach blends rigorous QA practices with DevOps principles enabling secure, scalable, and maintainable systems across cloud-native environments.
            </motion.p>

            <motion.div variants={itemVariants} className="flex gap-4 mb-8">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-10 h-10 bg-card shadow-md hover:shadow-lg text-muted-foreground hover:text-primary rounded-full transition-all duration-200 flex items-center justify-center border border-border"
                  aria-label={social.label}
                >
                  <social.icon size={18} />
                </motion.a>
              ))}
            </motion.div>

            <motion.div variants={itemVariants} className="flex flex-col sm:flex-row gap-4 mb-16">
              <MagneticButton
                href="#domains"
                className="inline-flex items-center gap-2"
              >
                View My Expertise
                <ExternalLink size={18} />
              </MagneticButton>

              <NeonButton
                color="purple"
                onClick={() => window.open('#contact', '_self')}
                className="inline-flex items-center gap-2"
              >
                Get In Touch
                <Mail size={18} />
              </NeonButton>
            </motion.div>
          </motion.div>

          <motion.div variants={containerVariants} className="lg:order-2 order-1 flex justify-center">
            <motion.div variants={itemVariants} className="relative">
              <motion.div
                animate={{ scale: [1, 1.02, 1] }}
                transition={{ duration: 8, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
                className="w-64 h-64 md:w-80 md:h-80 relative z-10"
              >
                <img src="/profile-pic.jpg" alt={name} className="w-full h-full object-cover rounded-full shadow-2xl" />
              </motion.div>

              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                className="absolute -inset-8 rounded-full border-2 border-dashed border-blue-300 z-0"
              />

              <motion.div
                animate={{ y: [0, -8, 0] }}
                transition={{ duration: 6, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
                className="absolute top-0 right-0 w-20 h-20 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 z-0"
              />

              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 7, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
                className="absolute bottom-0 left-0 w-20 h-20 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 z-0"
              />
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5, duration: 0.5 }}
        className="absolute bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center"
      >
        <span className="text-sm text-muted-foreground mb-2">Scroll to explore</span>
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="p-2 rounded-full border-2 border-border text-muted-foreground hover:border-primary hover:text-primary transition-colors duration-200 cursor-pointer"
          onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}
        >
          <ArrowDown size={20} />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default memo(Hero);
