"use client";

import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';

interface MagneticButtonProps {
  children: React.ReactNode;
  className?: string;
  strength?: number;
  onClick?: () => void;
  href?: string;
  target?: string;
  rel?: string;
}

export const MagneticButton: React.FC<MagneticButtonProps> = ({
  children,
  className = '',
  strength = 0.3,
  onClick,
  href,
  target,
  rel
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!ref.current) return;

    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const deltaX = (e.clientX - centerX) * strength;
    const deltaY = (e.clientY - centerY) * strength;

    setPosition({ x: deltaX, y: deltaY });
  };

  const handleMouseLeave = () => {
    setPosition({ x: 0, y: 0 });
  };

  const content = (
    <motion.div
      ref={ref}
      className={`relative inline-block cursor-pointer ${className}`}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      animate={{ x: position.x, y: position.y }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <motion.div
        className="relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 p-0.5 group"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        {/* Animated background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600"
          animate={{
            backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{
            backgroundSize: "200% 200%"
          }}
        />
        
        {/* Content */}
        <div className="relative bg-white dark:bg-gray-900 rounded-lg px-6 py-3 font-medium text-gray-900 dark:text-white transition-colors duration-300 group-hover:bg-transparent group-hover:text-white">
          {children}
        </div>

        {/* Shine effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 opacity-0 group-hover:opacity-100"
          animate={{
            x: ["-100%", "100%"]
          }}
          transition={{
            duration: 0.8,
            ease: "easeInOut"
          }}
        />
      </motion.div>
    </motion.div>
  );

  if (href) {
    return (
      <a href={href} target={target} rel={rel} className="inline-block">
        {content}
      </a>
    );
  }

  return content;
};

// Glitch button effect
export const GlitchButton: React.FC<{
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}> = ({ children, className = '', onClick }) => {
  const [isGlitching, setIsGlitching] = useState(false);

  const handleClick = () => {
    setIsGlitching(true);
    setTimeout(() => setIsGlitching(false), 500);
    onClick?.();
  };

  return (
    <motion.button
      className={`relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium ${className}`}
      onClick={handleClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <span className={`relative z-10 ${isGlitching ? 'animate-pulse' : ''}`}>
        {children}
      </span>
      
      {/* Glitch layers */}
      {isGlitching && (
        <>
          <motion.div
            className="absolute inset-0 bg-red-500 mix-blend-multiply"
            animate={{
              x: [0, -2, 2, 0],
              opacity: [0, 0.7, 0.3, 0]
            }}
            transition={{ duration: 0.1, repeat: 5 }}
          />
          <motion.div
            className="absolute inset-0 bg-cyan-500 mix-blend-multiply"
            animate={{
              x: [0, 2, -2, 0],
              opacity: [0, 0.7, 0.3, 0]
            }}
            transition={{ duration: 0.1, repeat: 5, delay: 0.05 }}
          />
        </>
      )}
    </motion.button>
  );
};

// Neon glow button
export const NeonButton: React.FC<{
  children: React.ReactNode;
  className?: string;
  color?: 'blue' | 'purple' | 'green' | 'pink';
  onClick?: () => void;
}> = ({ children, className = '', color = 'blue', onClick }) => {
  const colorClasses = {
    blue: 'from-blue-500 to-cyan-500 shadow-blue-500/50',
    purple: 'from-purple-500 to-pink-500 shadow-purple-500/50',
    green: 'from-green-500 to-emerald-500 shadow-green-500/50',
    pink: 'from-pink-500 to-rose-500 shadow-pink-500/50'
  };

  return (
    <motion.button
      className={`relative px-6 py-3 bg-gradient-to-r ${colorClasses[color]} text-white font-medium rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-2xl ${className}`}
      onClick={onClick}
      whileHover={{ 
        scale: 1.05,
        boxShadow: "0 0 30px rgba(59, 130, 246, 0.5)"
      }}
      whileTap={{ scale: 0.95 }}
    >
      <span className="relative z-10">{children}</span>
      
      {/* Animated border */}
      <motion.div
        className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-white/20 to-transparent"
        animate={{
          x: ["-100%", "100%"]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </motion.button>
  );
};
