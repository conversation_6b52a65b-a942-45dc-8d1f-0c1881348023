import React, { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import * as Accordion from '@radix-ui/react-accordion';
import {
  Code, Palette, Zap, Heart, Award, BookOpen, Shield, Settings, ChevronDown
} from 'lucide-react';

const About: React.FC = () => {
  const containerVariants = useMemo(() => ({
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.2,
        staggerChildren: 0.15,
      },
    },
  }), []);

  const itemVariants = useMemo(() => ({
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.4 },
    },
  }), []);

  const highlights = [
    { icon: Code, title: 'Clean Code', description: 'Writing maintainable, scalable, and efficient code that stands the test of time.' },
    { icon: Palette, title: 'Design Thinking', description: 'Creating intuitive user experiences with attention to detail and aesthetic appeal.' },
    { icon: Zap, title: 'Performance', description: 'Optimizing applications for speed, accessibility, and cross-platform compatibility.' },
    { icon: Heart, title: 'Passion', description: 'Genuinely passionate about technology and its power to solve real-world problems.' },
    { icon: Award, title: 'Quality', description: 'Committed to delivering high-quality solutions that exceed client expectations.' },
    { icon: BookOpen, title: 'Continuous Learning', description: 'Always expanding my knowledge and staying current with industry best practices.' },
    { icon: Shield, title: 'Security', description: 'Building secure systems with robust authentication, authorization, and data protection.' },
    { icon: Settings, title: 'Automation Framework Development', description: 'Creating comprehensive automation frameworks that improve testing efficiency and reliability.' }
  ];

  return (
    <section id="about" className="py-20 bg-background relative">
      <BackgroundPattern />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <SectionHeader itemVariants={itemVariants} />

          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <ImageColumn itemVariants={itemVariants} />
            <ContentColumn itemVariants={itemVariants} />
          </div>

          <HighlightsGrid highlights={highlights} itemVariants={itemVariants} />
          <ExperienceAccordion itemVariants={itemVariants} />
          <CallToAction itemVariants={itemVariants} />
        </motion.div>
      </div>
    </section>
  );
};

const BackgroundPattern: React.FC = () => (
  <div className="absolute inset-0 opacity-5 pointer-events-none">
    <div className="absolute top-0 left-0 w-full h-full" style={{
      backgroundImage: 'radial-gradient(circle at 25px 25px, #0072c6 2px, transparent 0)',
      backgroundSize: '50px 50px'
    }} />
  </div>
);

const SectionHeader: React.FC<{ itemVariants: any }> = ({ itemVariants }) => (
  <motion.div variants={itemVariants} className="text-center mb-16">
    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
      About <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Me</span>
    </h2>
    <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
      Passionate developer with a love for creating digital experiences that make a difference
    </p>
  </motion.div>
);

const ImageColumn: React.FC<{ itemVariants: any }> = ({ itemVariants }) => {
  const name = "Adil Faiyaz";
  const initials = name.split(' ').map(name => name.charAt(0)).join('');

  return (
    <motion.div variants={itemVariants} className="flex justify-center lg:justify-start">
      <div className="relative">
        <motion.div
          animate={{ y: [0, -6, 0] }}
          transition={{ duration: 6, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
          className="w-80 h-80 relative z-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-2xl flex items-center justify-center"
        >
          <div className="w-32 h-32 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white font-bold text-4xl">
            {initials}
          </div>
        </motion.div>

        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
          className="absolute -inset-4 rounded-2xl border-2 border-dashed border-primary-300 z-0"
        />

        <motion.div
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 8, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
          className="absolute top-4 right-4 w-16 h-16 bg-accent-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 z-0"
        />

        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 7, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
          className="absolute bottom-4 left-4 w-16 h-16 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 z-0"
        />
      </div>
    </motion.div>
  );
};

const ContentColumn: React.FC<{ itemVariants: any }> = ({ itemVariants }) => {
  const bio = "I'm a Senior QA Engineer with 10 years of experience in test automation, cloud technologies, and DevOps. My expertise spans across microservices architecture, Kafka, Go/Python/Java development, AWS, Kubernetes, and security testing. I'm passionate about building reliable, scalable systems and helping teams deliver quality software faster.";

  return (
    <motion.div variants={itemVariants} className="space-y-6">
      <div>
        <h3 className="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
          My Journey
        </h3>
        <p className="text-gray-600 dark:text-gray-300 leading-relaxed text-lg">
          {bio}
        </p>
      </div>

      <div className="space-y-4">
        <h4 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Core Values
        </h4>
        <ul className="space-y-2 text-gray-600 dark:text-gray-300">
          <li className="flex items-center">
            <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
            Quality-first approach to software development
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
            Continuous learning and adaptation
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
            Collaboration and knowledge sharing
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
            Innovation through automation
          </li>
        </ul>
      </div>
    </motion.div>
  );
};

const HighlightsGrid: React.FC<{ highlights: any[], itemVariants: any }> = ({ highlights, itemVariants }) => (
  <motion.div variants={itemVariants} className="mt-20">
    <h3 className="text-2xl font-semibold text-center mb-12 text-gray-800 dark:text-gray-200">
      What Drives Me
    </h3>
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      {highlights.map((highlight, index) => (
        <motion.div
          key={highlight.title}
          variants={itemVariants}
          className="text-center p-6 rounded-xl bg-card hover:shadow-lg transition-all duration-300 border border-border hover:border-primary hover:scale-105"
        >
          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-4">
            <highlight.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h4 className="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">
            {highlight.title}
          </h4>
          <p className="text-gray-600 dark:text-gray-300 text-sm">
            {highlight.description}
          </p>
        </motion.div>
      ))}
    </div>
  </motion.div>
);

const ExperienceAccordion: React.FC<{ itemVariants: any }> = ({ itemVariants }) => {
  const experienceItems = [
    {
      id: "automation",
      title: "Test Automation Excellence",
      content: "10+ years of experience building robust automation frameworks using Selenium, Playwright, and custom solutions. Specialized in API testing, performance testing, and CI/CD integration with comprehensive reporting and analytics."
    },
    {
      id: "cloud",
      title: "Cloud & DevOps Mastery",
      content: "Expert in AWS cloud services (EC2, ECS, EKS, Lambda, S3, DynamoDB), Kubernetes orchestration, Infrastructure-as-Code with Terraform and Pulumi, and implementing secure, scalable cloud architectures."
    },
    {
      id: "security",
      title: "Security & Compliance Focus",
      content: "Deep expertise in security testing, compliance frameworks (SOC2, NIST, HIPAA), SAST/DAST implementation, IAM systems, and building secure multi-tenant applications with proper access controls."
    },
    {
      id: "leadership",
      title: "Technical Leadership",
      content: "Led cross-functional teams in implementing quality engineering practices, mentored junior developers, and established testing standards that improved deployment confidence and reduced production issues by 60%."
    }
  ];

  return (
    <motion.div variants={itemVariants} className="mt-16">
      <h3 className="text-2xl font-bold text-center mb-8 text-gray-800 dark:text-gray-200">
        Professional Journey
      </h3>
      <Accordion.Root type="single" collapsible className="w-full max-w-3xl mx-auto">
        {experienceItems.map((item, index) => (
          <Accordion.Item key={item.id} value={item.id} className="border-b border-gray-200 dark:border-gray-700">
            <Accordion.Header>
              <Accordion.Trigger className="flex w-full items-center justify-between py-4 px-6 text-left hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors group">
                <span className="text-lg font-semibold text-gray-800 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {item.title}
                </span>
                <ChevronDown className="h-5 w-5 text-gray-500 transition-transform duration-200 group-data-[state=open]:rotate-180" />
              </Accordion.Trigger>
            </Accordion.Header>
            <Accordion.Content className="overflow-hidden data-[state=open]:animate-accordion-down data-[state=closed]:animate-accordion-up">
              <div className="px-6 pb-4">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {item.content}
                </p>
              </div>
            </Accordion.Content>
          </Accordion.Item>
        ))}
      </Accordion.Root>
    </motion.div>
  );
};

const CallToAction: React.FC<{ itemVariants: any }> = ({ itemVariants }) => (
  <motion.div variants={itemVariants} className="text-center mt-16">
    <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
      Ready to work together? Let's create something amazing!
    </p>
    <motion.a
      href="#contact"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:shadow-lg transition-all duration-200"
    >
      Get In Touch
    </motion.a>
  </motion.div>
);

export default memo(About);
